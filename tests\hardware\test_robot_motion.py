# tests/hardware/test_robot_motion.py

import sys
import os
import time

# 将项目根目录添加到Python路径中
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
sys.path.insert(0, PROJECT_ROOT)

# 将 lib 目录也明确添加到路径中
LIB_PATH = os.path.join(PROJECT_ROOT, 'lib')
if LIB_PATH not in sys.path:
    sys.path.insert(0, LIB_PATH)

# 导入配置和接口
from config import ROBOT_IP, ROBOT_PORT
from src.hardware.robot_interface import RobotInterface

# 确保能够导入nrc_interface
try:
    import nrc_interface as nrc
    print("成功导入nrc_interface模块")
except ImportError as e:
    print(f"导入nrc_interface失败: {e}")
    print(f"当前Python路径: {sys.path}")
    print(f"尝试从以下位置导入: {LIB_PATH}")
    # 尝试直接从lib目录导入
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../lib')))
    try:
        import nrc_interface as nrc
        print("通过绝对路径成功导入nrc_interface模块")
    except ImportError as e:
        print(f"再次尝试导入失败: {e}")
        print("请确保lib目录中包含nrc_interface.py和nrc_host.pyd文件")

class RobotMotionTester:
    """机械臂运动测试器 - 测试所有运动控制功能"""
    
    def __init__(self, robot_interface: RobotInterface):
        self.robot = robot_interface
        self.socket_fd = robot_interface.socket_fd
    
    def _safe_execute_motion(self, motion_func, *args, motion_name="未知运动"):
        """安全地执行运动指令"""
        try:
            print(f"执行{motion_name}...")
            result = motion_func(self.socket_fd, *args)
            print(f"{motion_name}执行结果: {result}")
            return result
        except Exception as e:
            print(f"{motion_name}执行失败: {e}")
            return -1
    
    def test_basic_movements(self):
        """测试基本运动功能"""
        print("\n=== 基本运动测试 ===")
        
        # 回零点
        self._safe_execute_motion(nrc.robot_go_home, motion_name="回零点")
        
        # 回复位点
        self._safe_execute_motion(nrc.robot_go_to_reset_position, motion_name="回复位点")
    
    def test_joint_motion(self):
        """测试关节运动 (movej)"""
        print("\n=== 关节运动测试 ===")
        
        try:
            # 创建关节运动指令
            move_cmd = nrc.MoveCmd()
            
            # 设置目标位置类型为关节坐标
            move_cmd.targetPosType = 0  # 0表示关节坐标
            
            # 设置目标关节位置 (示例：6轴机器人的关节角度，单位：度)
            target_joints = nrc.VectorDouble()
            joint_angles = [0.0, -90.0, 90.0, 0.0, 90.0, 0.0]  # 示例关节角度
            for angle in joint_angles:
                target_joints.append(angle)
            move_cmd.targetPosValue = target_joints
            
            # 设置运动参数
            move_cmd.vel = 50        # 速度百分比 (1-100)
            move_cmd.acc = 50        # 加速度百分比 (1-100)
            move_cmd.dec = 50        # 减速度百分比 (1-100)
            move_cmd.coord = 0       # 坐标系 (0=关节坐标系)
            move_cmd.blendR = 0.0    # 混合半径
            move_cmd.blendType = 0   # 混合类型
            
            print(f"目标关节位置: {joint_angles}")
            print(f"运动参数: 速度={move_cmd.vel}%, 加速度={move_cmd.acc}%, 减速度={move_cmd.dec}%")
            
            # 执行关节运动
            self._safe_execute_motion(nrc.robot_movej, move_cmd, motion_name="关节运动(movej)")
            
        except Exception as e:
            print(f"关节运动测试失败: {e}")
    
    def test_linear_motion(self):
        """测试直线运动 (movel)"""
        print("\n=== 直线运动测试 ===")
        
        try:
            # 创建直线运动指令
            move_cmd = nrc.MoveCmd()
            
            # 设置目标位置类型为笛卡尔坐标
            move_cmd.targetPosType = 1  # 1表示笛卡尔坐标
            
            # 设置目标笛卡尔位置 (X, Y, Z, RX, RY, RZ)
            target_pos = nrc.VectorDouble()
            cartesian_pos = [300.0, 0.0, 400.0, 180.0, 0.0, 0.0]  # 示例位置 (mm, 度)
            for pos in cartesian_pos:
                target_pos.append(pos)
            move_cmd.targetPosValue = target_pos
            
            # 设置运动参数
            move_cmd.vel = 100       # 线速度 (mm/s)
            move_cmd.acc = 50        # 加速度百分比
            move_cmd.dec = 50        # 减速度百分比
            move_cmd.coord = 1       # 坐标系 (1=笛卡尔坐标系)
            move_cmd.blendR = 0.0    # 混合半径
            move_cmd.blendType = 0   # 混合类型
            
            print(f"目标笛卡尔位置: {cartesian_pos}")
            print(f"运动参数: 线速度={move_cmd.vel}mm/s, 加速度={move_cmd.acc}%, 减速度={move_cmd.dec}%")
            
            # 执行直线运动
            self._safe_execute_motion(nrc.robot_movel, move_cmd, motion_name="直线运动(movel)")
            
        except Exception as e:
            print(f"直线运动测试失败: {e}")
    
    def test_circular_motion(self):
        """测试圆弧运动 (movec)"""
        print("\n=== 圆弧运动测试 ===")
        
        try:
            # 设置圆弧运动的三个点
            # 起始点 (当前位置)
            pos1 = nrc.VectorDouble()
            start_pos = [300.0, 0.0, 400.0, 180.0, 0.0, 0.0]
            for pos in start_pos:
                pos1.append(pos)
            
            # 中间点 (圆弧上的点)
            pos2 = nrc.VectorDouble()
            mid_pos = [350.0, 50.0, 400.0, 180.0, 0.0, 0.0]
            for pos in mid_pos:
                pos2.append(pos)
            
            # 终点
            pos3 = nrc.VectorDouble()
            end_pos = [400.0, 0.0, 400.0, 180.0, 0.0, 0.0]
            for pos in end_pos:
                pos3.append(pos)
            
            # 运动参数
            vel = 50        # 速度 (mm/s)
            coord = 1       # 坐标系 (1=笛卡尔坐标系)
            acc = 50        # 加速度百分比
            dec = 50        # 减速度百分比
            
            print(f"起始点: {start_pos}")
            print(f"中间点: {mid_pos}")
            print(f"终点: {end_pos}")
            print(f"运动参数: 速度={vel}mm/s, 加速度={acc}%, 减速度={dec}%")
            
            # 执行圆弧运动
            self._safe_execute_motion(nrc.robot_movec, pos1, pos2, pos3, vel, coord, acc, dec, 
                                    motion_name="圆弧运动(movec)")
            
        except Exception as e:
            print(f"圆弧运动测试失败: {e}")
    
    def test_full_circle_motion(self):
        """测试整圆运动 (moveca)"""
        print("\n=== 整圆运动测试 ===")
        
        try:
            # 设置整圆运动的三个点
            # 起始点
            pos1 = nrc.VectorDouble()
            start_pos = [300.0, 0.0, 400.0, 180.0, 0.0, 0.0]
            for pos in start_pos:
                pos1.append(pos)
            
            # 圆上第二点
            pos2 = nrc.VectorDouble()
            second_pos = [350.0, 50.0, 400.0, 180.0, 0.0, 0.0]
            for pos in second_pos:
                pos2.append(pos)
            
            # 圆上第三点
            pos3 = nrc.VectorDouble()
            third_pos = [300.0, 100.0, 400.0, 180.0, 0.0, 0.0]
            for pos in third_pos:
                pos3.append(pos)
            
            # 运动参数
            vel = 30        # 速度 (mm/s)
            coord = 1       # 坐标系
            acc = 50        # 加速度百分比
            dec = 50        # 减速度百分比
            
            print(f"整圆第一点: {start_pos}")
            print(f"整圆第二点: {second_pos}")
            print(f"整圆第三点: {third_pos}")
            print(f"运动参数: 速度={vel}mm/s, 加速度={acc}%, 减速度={dec}%")
            
            # 执行整圆运动
            self._safe_execute_motion(nrc.robot_moveca, pos1, pos2, pos3, vel, coord, acc, dec,
                                    motion_name="整圆运动(moveca)")
            
        except Exception as e:
            print(f"整圆运动测试失败: {e}")

    def test_spline_motion(self):
        """测试样条曲线运动 (moves)"""
        print("\n=== 样条曲线运动测试 ===")

        try:
            # 创建多个路径点
            path_points = nrc.VectorVectorDouble()

            # 定义路径点 (多个笛卡尔坐标点)
            points = [
                [300.0, 0.0, 400.0, 180.0, 0.0, 0.0],
                [320.0, 20.0, 410.0, 180.0, 0.0, 0.0],
                [340.0, 30.0, 420.0, 180.0, 0.0, 0.0],
                [360.0, 25.0, 415.0, 180.0, 0.0, 0.0],
                [380.0, 10.0, 405.0, 180.0, 0.0, 0.0]
            ]

            for point in points:
                pos = nrc.VectorDouble()
                for coord in point:
                    pos.append(coord)
                path_points.append(pos)

            # 运动参数
            vel = 50        # 速度 (mm/s)
            coord = 1       # 坐标系
            acc = 50        # 加速度百分比
            dec = 50        # 减速度百分比

            print(f"样条路径点数: {len(points)}")
            for i, point in enumerate(points):
                print(f"  点{i+1}: {point}")
            print(f"运动参数: 速度={vel}mm/s, 加速度={acc}%, 减速度={dec}%")

            # 执行样条曲线运动
            self._safe_execute_motion(nrc.robot_moves, path_points, vel, coord, acc, dec,
                                    motion_name="样条曲线运动(moves)")

        except Exception as e:
            print(f"样条曲线运动测试失败: {e}")

    def test_jogging_motion(self):
        """测试点动功能"""
        print("\n=== 点动测试 ===")

        try:
            # 测试各轴点动
            for axis in range(6):  # 假设6轴机器人
                print(f"测试轴{axis+1}点动...")

                # 正方向点动
                print(f"  轴{axis+1}正方向点动")
                self._safe_execute_motion(nrc.robot_start_jogging, axis, True,
                                        motion_name=f"轴{axis+1}正方向点动")

                # 模拟点动一段时间
                time.sleep(0.1)  # 点动0.1秒

                # 停止点动
                self._safe_execute_motion(nrc.robot_stop_jogging, axis,
                                        motion_name=f"停止轴{axis+1}点动")

                # 负方向点动
                print(f"  轴{axis+1}负方向点动")
                self._safe_execute_motion(nrc.robot_start_jogging, axis, False,
                                        motion_name=f"轴{axis+1}负方向点动")

                # 模拟点动一段时间
                time.sleep(0.1)  # 点动0.1秒

                # 停止点动
                self._safe_execute_motion(nrc.robot_stop_jogging, axis,
                                        motion_name=f"停止轴{axis+1}点动")

        except Exception as e:
            print(f"点动测试失败: {e}")

    def test_external_axis_motion(self):
        """测试外部轴运动"""
        print("\n=== 外部轴运动测试 ===")

        try:
            # 外部轴关节运动
            print("外部轴关节运动测试:")
            move_cmd = nrc.MoveCmd()
            move_cmd.targetPosType = 0  # 关节坐标

            # 设置外部轴目标位置
            target_pos = nrc.VectorDouble()
            external_joints = [0.0, 90.0]  # 示例：2个外部轴
            for pos in external_joints:
                target_pos.append(pos)
            move_cmd.targetPosValue = target_pos

            move_cmd.vel = 50
            move_cmd.acc = 50
            move_cmd.dec = 50
            move_cmd.coord = 0

            print(f"外部轴目标位置: {external_joints}")
            self._safe_execute_motion(nrc.robot_extra_movej, move_cmd,
                                    motion_name="外部轴关节运动")

            # 外部轴直线运动
            print("外部轴直线运动测试:")
            move_cmd.targetPosType = 1  # 笛卡尔坐标
            target_pos = nrc.VectorDouble()
            external_cart = [100.0, 200.0]  # 示例外部轴笛卡尔位置
            for pos in external_cart:
                target_pos.append(pos)
            move_cmd.targetPosValue = target_pos
            move_cmd.vel = 100  # mm/s

            print(f"外部轴笛卡尔位置: {external_cart}")
            self._safe_execute_motion(nrc.robot_extra_movel, move_cmd,
                                    motion_name="外部轴直线运动")

        except Exception as e:
            print(f"外部轴运动测试失败: {e}")

    def test_servo_motion(self):
        """测试伺服运动 (需要连接7000端口)"""
        print("\n=== 伺服运动测试 ===")

        try:
            # 注意：伺服运动需要连接到7000端口
            print("注意：伺服运动需要连接到机器人的7000端口")
            print("示例代码：fd7000 = connect_robot('192.168.1.13', '7000')")

            # 创建伺服运动参数
            servo_para = nrc.ServoMovePara()
            servo_para.clearBuffer = True
            servo_para.targetMode = 0  # 目标模式
            servo_para.sendMode = 0    # 发送模式

            # 设置目标位置
            target_pos = nrc.VectorDouble()
            servo_pos = [300.0, 0.0, 400.0, 180.0, 0.0, 0.0]
            for pos in servo_pos:
                target_pos.append(pos)
            servo_para.targetPos = target_pos

            servo_para.vel = 50.0      # 速度
            servo_para.acc = 50.0      # 加速度
            servo_para.dec = 50.0      # 减速度

            print(f"伺服目标位置: {servo_pos}")
            print(f"伺服参数: 速度={servo_para.vel}, 加速度={servo_para.acc}, 减速度={servo_para.dec}")
            print("警告：此测试需要7000端口连接，当前使用6001端口可能失败")

            # 执行伺服运动 (可能失败，因为端口不对)
            self._safe_execute_motion(nrc.servo_move, servo_para,
                                    motion_name="伺服运动(servo_move)")

        except Exception as e:
            print(f"伺服运动测试失败: {e}")
            print("这是预期的，因为伺服运动需要连接7000端口")


def test_robot_motion():
    """测试机器人运动控制功能"""
    robot = None
    try:
        print("=" * 60)
        print("机器人运动控制测试")
        print("=" * 60)
        print("警告：此测试包含实际的机器人运动指令！")
        print("请确保：")
        print("1. 机器人周围无障碍物")
        print("2. 机器人处于安全位置")
        print("3. 已做好急停准备")
        print("4. 运动参数已根据实际机器人调整")
        print("=" * 60)

        # 连接机器人
        print(f"正在连接机器人 {ROBOT_IP}:{ROBOT_PORT}...")
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)

        # 创建运动测试器
        motion_tester = RobotMotionTester(robot)

        # 执行各种运动测试
        print("\n开始运动测试...")

        # 基本运动测试
        motion_tester.test_basic_movements()

        # 关节运动测试
        motion_tester.test_joint_motion()

        # 直线运动测试
        motion_tester.test_linear_motion()

        # 圆弧运动测试
        motion_tester.test_circular_motion()

        # 整圆运动测试
        motion_tester.test_full_circle_motion()

        # 样条曲线运动测试
        motion_tester.test_spline_motion()

        # 点动测试
        motion_tester.test_jogging_motion()

        # 外部轴运动测试
        motion_tester.test_external_axis_motion()

        # 伺服运动测试
        motion_tester.test_servo_motion()

        print("\n" + "=" * 60)
        print("运动测试完成！")
        print("=" * 60)

    except RuntimeError as e:
        print(f"\n❌ 连接失败（这在没有实际硬件时是正常的）: {e}")
        print("测试框架工作正常，但需要实际的机器人硬件才能完成运动测试。")
        print("\n重要提醒：")
        print("- 运动测试包含实际的机器人运动指令")
        print("- 请在安全环境下进行测试")
        print("- 建议先在仿真环境中验证运动参数")

    except Exception as e:
        print(f"\n❌ 发生意外错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        if robot:
            robot.disconnect()


if __name__ == "__main__":
    test_robot_motion()
