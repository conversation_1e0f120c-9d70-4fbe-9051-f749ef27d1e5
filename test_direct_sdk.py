# test_direct_sdk.py - 直接使用SDK原始调用方式测试

import sys
import os

# 路径设置
PROJECT_ROOT = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, PROJECT_ROOT)
sys.path.insert(0, os.path.join(PROJECT_ROOT, 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

def test_direct_sdk():
    """直接使用SDK原始调用方式测试"""
    try:
        print("=" * 60)
        print("直接SDK调用测试")
        print("=" * 60)
        
        # 连接机器人
        print(f"连接机器人 {ROBOT_IP}:{ROBOT_PORT}...")
        socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
        
        if socket_fd < 0:
            print(f"连接失败，错误码: {socket_fd}")
            return
            
        print(f"连接成功，套接字: {socket_fd}")
        
        # 检查连接状态
        conn_status = nrc.get_connection_status(socket_fd)
        print(f"连接状态: {conn_status}")
        
        # 检查伺服状态 - 使用正确的方式（根据重要补充.md）
        print("\n检查伺服状态...")
        servo_status = 0  # 初始化为整数
        result = nrc.get_servo_state(socket_fd, servo_status)
        print(f"get_servo_state 返回: {result}")
        if isinstance(result, list) and len(result) > 1:
            actual_servo_status = result[1]  # 从列表第二个元素获取数据
            servo_names = {0: "停止", 1: "就绪", 2: "报警", 3: "运行"}
            servo_name = servo_names.get(actual_servo_status, f"未知({actual_servo_status})")
            print(f"伺服状态: {servo_name} (值: {actual_servo_status})")
        else:
            print(f"伺服状态获取失败: {result}")
            actual_servo_status = -1

        # 检查当前模式
        print("\n检查当前模式...")
        mode = 0  # 初始化为整数
        result = nrc.get_current_mode(socket_fd, mode)
        print(f"get_current_mode 返回: {result}")
        if isinstance(result, list) and len(result) > 1:
            actual_mode = result[1]
            mode_names = {0: "示教模式", 1: "运行模式", 2: "远程模式"}
            mode_name = mode_names.get(actual_mode, f"未知({actual_mode})")
            print(f"当前模式: {mode_name} (值: {actual_mode})")
        else:
            print(f"模式获取失败: {result}")
            actual_mode = -1

        # 检查运行状态
        print("\n检查运行状态...")
        running_status = 0  # 初始化为整数
        result = nrc.get_robot_running_state(socket_fd, running_status)
        print(f"get_robot_running_state 返回: {result}")
        if isinstance(result, list) and len(result) > 1:
            actual_running_status = result[1]
            print(f"运行状态: {actual_running_status}")
        else:
            print(f"运行状态获取失败: {result}")
            actual_running_status = -1
        
        # 尝试获取当前位置
        print("\n检查当前位置...")
        pos = nrc.VectorDouble()
        result = nrc.get_current_position(socket_fd, 0, pos)  # 0 = 关节坐标
        print(f"get_current_position 返回: {result}")
        print(f"位置数据长度: {len(pos)}")
        if len(pos) > 0:
            positions = [pos[i] for i in range(min(6, len(pos)))]
            print(f"关节位置: {positions}")
        
        # 尝试设置远程模式
        if actual_mode != 2:  # 如果不是远程模式
            print(f"\n尝试设置为远程模式...")
            result = nrc.set_current_mode(socket_fd, 2)  # 2 = 远程模式
            print(f"设置远程模式结果: {result}")

            if result == 0:
                # 重新检查模式
                mode_check = 0
                result = nrc.get_current_mode(socket_fd, mode_check)
                if isinstance(result, list) and len(result) > 1:
                    new_mode = result[1]
                    mode_name = mode_names.get(new_mode, f"未知({new_mode})")
                    print(f"设置后模式: {mode_name} (值: {new_mode})")
                    actual_mode = new_mode

        # 尝试设置伺服就绪
        if actual_servo_status == 0:  # 如果是停止状态
            print(f"\n尝试设置伺服就绪...")
            result = nrc.set_servo_state(socket_fd, 1)  # 1 = 就绪状态
            print(f"设置伺服就绪结果: {result}")

            if result == 0:
                # 重新检查伺服状态
                servo_check = 0
                result = nrc.get_servo_state(socket_fd, servo_check)
                if isinstance(result, list) and len(result) > 1:
                    new_servo_status = result[1]
                    servo_name = servo_names.get(new_servo_status, f"未知({new_servo_status})")
                    print(f"设置后伺服状态: {servo_name} (值: {new_servo_status})")
                    actual_servo_status = new_servo_status

        # 如果伺服就绪，尝试上电
        if actual_servo_status == 1:
            print("\n✅ 伺服已就绪，尝试上电...")
            result = nrc.set_servo_poweron(socket_fd)
            print(f"上电结果: {result}")

            if result == 0:
                print("✅ 上电成功！再次检查状态...")
                servo_status_new = 0  # 重置为整数
                result = nrc.get_servo_state(socket_fd, servo_status_new)
                if isinstance(result, list) and len(result) > 1:
                    new_servo_status = result[1]
                    servo_name = servo_names.get(new_servo_status, f"未知({new_servo_status})")
                    print(f"上电后伺服状态: {servo_name} (值: {new_servo_status})")

                    if new_servo_status == 3:
                        print("🎉 机器人已进入运行状态，可以执行运动控制！")
                else:
                    print(f"上电后状态检查失败: {result}")
            else:
                print(f"❌ 上电失败，错误码: {result}")
        elif actual_servo_status == 3:
            print("✅ 机器人已经处于运行状态！")
        elif actual_servo_status == 0:
            print("⚠️ 机器人处于停止状态，请在示教器上设置伺服就绪")
        elif actual_servo_status == 2:
            print("❌ 机器人处于报警状态，请先清除报警")
        else:
            print(f"❓ 未知的伺服状态: {actual_servo_status}")
        
        # 断开连接
        print("\n断开连接...")
        nrc.disconnect_robot(socket_fd)
        print("测试完成")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_direct_sdk()
